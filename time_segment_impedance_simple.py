#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时段区间阻抗系统简化版实现
基于历史交通数据的时段区间阻抗划分方法
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class TimeSegmentImpedanceSystem:
    """时段区间阻抗系统主类"""
    
    def __init__(self, confidence_level=0.95):
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level
        self.road_segments = {}
        self.original_data = None
        
    def load_and_preprocess_data(self, file_paths):
        """加载和预处理数据"""
        print("=== 开始数据预处理 ===")
        
        # 1. 加载数据
        all_data = []
        for file_path in file_paths:
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                all_data.append(df)
                print(f"成功加载文件: {file_path}, 记录数: {len(df)}")
            except Exception as e:
                print(f"加载文件失败 {file_path}: {e}")
                
        if not all_data:
            raise ValueError("没有成功加载任何数据文件")
            
        data = pd.concat(all_data, ignore_index=True)
        print(f"总计加载记录数: {len(data)}")
        
        # 2. 数据清洗
        data['采集时间'] = pd.to_datetime(data['采集时间'])
        data['小时'] = data['采集时间'].dt.hour
        data['分钟'] = data['采集时间'].dt.minute
        data['时间分钟'] = data['小时'] * 60 + data['分钟']
        data['速度'] = data['距离(米)'] / data['通行时间(秒)']
        
        # 3. 异常值处理
        cleaned_data = []
        outlier_count = 0
        
        for road_id in data['路段ID'].unique():
            road_data = data[data['路段ID'] == road_id].copy()
            
            # IQR异常值检测
            Q1 = road_data['通行时间(秒)'].quantile(0.25)
            Q3 = road_data['通行时间(秒)'].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 移除异常值
            before_count = len(road_data)
            road_data = road_data[
                (road_data['通行时间(秒)'] >= lower_bound) & 
                (road_data['通行时间(秒)'] <= upper_bound)
            ]
            outlier_count += before_count - len(road_data)
            
            if len(road_data) > 0:
                cleaned_data.append(road_data)
        
        self.original_data = pd.concat(cleaned_data, ignore_index=True)
        print(f"移除 {outlier_count} 个异常值")
        print(f"清洗后数据记录数: {len(self.original_data)}")
        
        # 4. 时间间隔聚合
        self.original_data['时间间隔'] = self.original_data['时间分钟'] // 15  # 15分钟间隔
        
        return self.original_data
        
    def partition_time_segments(self, road_data, significance_level=0.05):
        """基于统计检验的时段划分"""
        time_intervals = sorted(road_data['时间间隔'].unique())
        
        if len(time_intervals) < 2:
            return [time_intervals]
            
        segments = []
        current_segment = [time_intervals[0]]
        
        for i in range(1, len(time_intervals)):
            current_interval = time_intervals[i]
            
            # 获取当前间隔和之前间隔的通行时间数据
            current_times = road_data[road_data['时间间隔'] == current_interval]['通行时间(秒)'].values
            previous_times = road_data[road_data['时间间隔'].isin(current_segment)]['通行时间(秒)'].values
            
            # t检验判断是否有显著差异
            if len(current_times) > 1 and len(previous_times) > 1:
                try:
                    t_stat, p_value = stats.ttest_ind(current_times, previous_times)
                    
                    if p_value < significance_level:
                        # 显著差异，开始新时段
                        segments.append(current_segment)
                        current_segment = [current_interval]
                    else:
                        # 无显著差异，合并到当前时段
                        current_segment.append(current_interval)
                except:
                    current_segment.append(current_interval)
            else:
                current_segment.append(current_interval)
                
        segments.append(current_segment)
        return segments
        
    def calculate_impedance_interval(self, travel_times):
        """计算阻抗区间"""
        if len(travel_times) < 2:
            return None
            
        travel_times = np.array(travel_times)
        n = len(travel_times)
        mean = np.mean(travel_times)
        std = np.std(travel_times, ddof=1)
        
        # 基于分位数的区间（更稳健）
        percentile_interval = {
            'lower': np.percentile(travel_times, (self.alpha/2) * 100),
            'upper': np.percentile(travel_times, (1 - self.alpha/2) * 100),
            'width': np.percentile(travel_times, (1 - self.alpha/2) * 100) - np.percentile(travel_times, (self.alpha/2) * 100)
        }
        
        # 基于正态分布的置信区间
        t_critical = stats.t.ppf(1 - self.alpha/2, df=n-1)
        margin_error = t_critical * std / np.sqrt(n)
        
        normal_interval = {
            'lower': max(0, mean - margin_error),
            'upper': mean + margin_error,
            'width': 2 * margin_error
        }
        
        return {
            'percentile': percentile_interval,
            'normal': normal_interval,
            'statistics': {
                'mean': mean,
                'std': std,
                'n': n,
                'cv': std / mean if mean > 0 else float('inf'),
                'median': np.median(travel_times)
            }
        }
        
    def build_system(self, data_files):
        """构建完整的时段区间阻抗系统"""
        print("=== 构建时段区间阻抗系统 ===")
        
        # 1. 数据预处理
        self.load_and_preprocess_data(data_files)
        
        # 2. 为每条道路进行时段划分和阻抗计算
        road_ids = self.original_data['路段ID'].unique()
        print(f"\n开始处理 {len(road_ids)} 个路段...")
        
        for i, road_id in enumerate(road_ids):
            if (i + 1) % 10 == 0:
                print(f"已处理 {i+1}/{len(road_ids)} 个路段")
                
            road_data = self.original_data[self.original_data['路段ID'] == road_id]
            
            if len(road_data) < 10:  # 数据太少，跳过
                continue
                
            # 时段划分
            segments = self.partition_time_segments(road_data)
            
            # 计算每个时段的阻抗区间
            road_intervals = {}
            for j, segment_intervals in enumerate(segments):
                segment_data = road_data[road_data['时间间隔'].isin(segment_intervals)]
                
                if len(segment_data) < 3:  # 时段数据太少
                    continue
                    
                travel_times = segment_data['通行时间(秒)'].values
                interval_result = self.calculate_impedance_interval(travel_times)
                
                if interval_result:
                    start_time = min(segment_intervals) * 15  # 转换为分钟
                    end_time = (max(segment_intervals) + 1) * 15
                    
                    road_intervals[f'segment_{j}'] = {
                        'time_intervals': segment_intervals,
                        'impedance_intervals': interval_result,
                        'start_time': start_time,
                        'end_time': end_time,
                        'start_time_str': f"{start_time//60:02d}:{start_time%60:02d}",
                        'end_time_str': f"{end_time//60:02d}:{end_time%60:02d}"
                    }
            
            if road_intervals:
                self.road_segments[road_id] = road_intervals
                
        print(f"成功处理 {len(self.road_segments)} 个路段")
        
    def get_impedance_interval(self, road_id, current_time_minutes, method='percentile'):
        """获取指定道路在指定时间的阻抗区间"""
        if road_id not in self.road_segments:
            return None
            
        road_data = self.road_segments[road_id]
        
        for segment_info in road_data.values():
            if segment_info['start_time'] <= current_time_minutes < segment_info['end_time']:
                return segment_info['impedance_intervals'][method]
                
        return None
        
    def compare_with_traditional_method(self, road_id):
        """与传统区间阻抗方法对比"""
        if road_id not in self.road_segments:
            return None
            
        # 获取原始数据
        road_data = self.original_data[self.original_data['路段ID'] == road_id]['通行时间(秒)']
        
        if len(road_data) == 0:
            return None
            
        # 传统方法：整体区间
        traditional_interval = {
            'lower': road_data.quantile(0.025),
            'upper': road_data.quantile(0.975),
            'width': road_data.quantile(0.975) - road_data.quantile(0.025)
        }
        
        # 时段区间方法：各时段区间
        segment_intervals = []
        for segment_info in self.road_segments[road_id].values():
            interval_data = segment_info['impedance_intervals']['percentile']
            segment_intervals.append({
                'lower': interval_data['lower'],
                'upper': interval_data['upper'],
                'width': interval_data['width'],
                'time_range': f"{segment_info['start_time_str']}-{segment_info['end_time_str']}"
            })
            
        # 计算改进指标
        avg_segment_width = np.mean([seg['width'] for seg in segment_intervals])
        width_reduction = (traditional_interval['width'] - avg_segment_width) / traditional_interval['width'] * 100
        
        return {
            'road_id': road_id,
            'traditional': traditional_interval,
            'segments': segment_intervals,
            'improvement': {
                'width_reduction_percent': width_reduction,
                'avg_segment_width': avg_segment_width,
                'num_segments': len(segment_intervals)
            }
        }
        
    def generate_summary_report(self):
        """生成摘要报告"""
        print("\n" + "="*60)
        print("时段区间阻抗系统分析报告")
        print("="*60)
        
        print(f"\n1. 系统概览")
        print(f"   处理路段数量: {len(self.road_segments)}")
        print(f"   原始数据记录数: {len(self.original_data)}")
        print(f"   数据时间范围: {self.original_data['采集时间'].min()} 到 {self.original_data['采集时间'].max()}")
        
        # 统计时段数量
        total_segments = sum(len(segments) for segments in self.road_segments.values())
        avg_segments = total_segments / len(self.road_segments) if self.road_segments else 0
        
        print(f"\n2. 时段划分统计")
        print(f"   总时段数量: {total_segments}")
        print(f"   平均每路段时段数: {avg_segments:.1f}")
        
        # 计算改进效果
        improvements = []
        for road_id in list(self.road_segments.keys())[:10]:  # 取前10个路段进行分析
            comparison = self.compare_with_traditional_method(road_id)
            if comparison and comparison['improvement']['width_reduction_percent'] > 0:
                improvements.append(comparison['improvement']['width_reduction_percent'])
        
        if improvements:
            print(f"\n3. 改进效果（基于前10个路段样本）")
            print(f"   平均区间宽度减少: {np.mean(improvements):.1f}%")
            print(f"   最大区间宽度减少: {np.max(improvements):.1f}%")
            print(f"   最小区间宽度减少: {np.min(improvements):.1f}%")
        
        # 详细路段示例
        print(f"\n4. 路段详细示例（前5个路段）")
        sample_roads = list(self.road_segments.keys())[:5]
        
        for road_id in sample_roads:
            print(f"\n   路段 {road_id}:")
            road_data = self.road_segments[road_id]
            print(f"     时段数量: {len(road_data)}")
            
            for segment_name, segment_info in road_data.items():
                stats = segment_info['impedance_intervals']['statistics']
                interval = segment_info['impedance_intervals']['percentile']
                print(f"     {segment_info['start_time_str']}-{segment_info['end_time_str']}: "
                      f"均值={stats['mean']:.1f}秒, "
                      f"区间=[{interval['lower']:.1f}, {interval['upper']:.1f}]秒")
        
        print(f"\n5. 实时查询示例")
        test_times = [7*60+30, 8*60, 8*60+30]  # 7:30, 8:00, 8:30
        
        for road_id in sample_roads[:3]:
            print(f"\n   路段 {road_id}:")
            for time_minutes in test_times:
                hour = time_minutes // 60
                minute = time_minutes % 60
                interval = self.get_impedance_interval(road_id, time_minutes)
                
                if interval:
                    print(f"     {hour:02d}:{minute:02d} - 阻抗区间: [{interval['lower']:.1f}, {interval['upper']:.1f}]秒")
                else:
                    print(f"     {hour:02d}:{minute:02d} - 无数据")

def main():
    """主函数"""
    print("时段区间阻抗系统演示")
    print("=" * 50)
    
    # 数据文件路径
    data_files = [
        '采集数据/早高峰/augment7.28早高峰（28晚改的）.csv',
        '采集数据/早高峰/augment7.29早高峰.csv',
        '采集数据/早高峰/augment7.30早高峰.csv',
        '采集数据/早高峰/augment7.31早高峰.csv'
    ]
    
    # 检查文件是否存在
    import os
    existing_files = [f for f in data_files if os.path.exists(f)]
    
    if not existing_files:
        print("错误：找不到数据文件")
        return
        
    print(f"找到 {len(existing_files)} 个数据文件")
    
    # 创建系统实例
    system = TimeSegmentImpedanceSystem(confidence_level=0.95)
    
    try:
        # 构建系统
        system.build_system(existing_files)
        
        # 生成报告
        system.generate_summary_report()
        
        print("\n系统构建完成！")
        
    except Exception as e:
        print(f"系统构建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
