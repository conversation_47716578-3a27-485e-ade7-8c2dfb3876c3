#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时段区间阻抗系统核心实现（无可视化依赖）
基于历史交通数据的时段区间阻抗划分方法
"""

import pandas as pd
import numpy as np
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TrafficDataPreprocessor:
    """交通数据预处理器"""
    
    def __init__(self):
        self.data = None
        self.cleaned_data = None
        
    def load_data(self, file_paths):
        """加载多个CSV文件的交通数据"""
        all_data = []
        
        for file_path in file_paths:
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                all_data.append(df)
                print(f"成功加载文件: {file_path}, 记录数: {len(df)}")
            except Exception as e:
                print(f"加载文件失败 {file_path}: {e}")
                
        if all_data:
            self.data = pd.concat(all_data, ignore_index=True)
            print(f"总计加载记录数: {len(self.data)}")
        else:
            raise ValueError("没有成功加载任何数据文件")
            
    def clean_data(self):
        """数据清洗和预处理"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        self.cleaned_data = self.data.copy()
        
        # 1. 处理时间字段
        self.cleaned_data['采集时间'] = pd.to_datetime(self.cleaned_data['采集时间'])
        
        # 2. 提取时间特征
        self.cleaned_data['小时'] = self.cleaned_data['采集时间'].dt.hour
        self.cleaned_data['分钟'] = self.cleaned_data['采集时间'].dt.minute
        self.cleaned_data['星期'] = self.cleaned_data['采集时间'].dt.weekday
        self.cleaned_data['时间分钟'] = self.cleaned_data['小时'] * 60 + self.cleaned_data['分钟']
        
        # 3. 计算速度 (米/秒)
        self.cleaned_data['速度'] = self.cleaned_data['距离(米)'] / self.cleaned_data['通行时间(秒)']
        
        # 4. 异常值检测和处理 (使用IQR方法)
        outlier_count = 0
        for road_id in self.cleaned_data['路段ID'].unique():
            road_mask = self.cleaned_data['路段ID'] == road_id
            road_data = self.cleaned_data[road_mask]['通行时间(秒)']
            
            Q1 = road_data.quantile(0.25)
            Q3 = road_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 标记异常值
            outlier_mask = road_mask & (
                (self.cleaned_data['通行时间(秒)'] < lower_bound) | 
                (self.cleaned_data['通行时间(秒)'] > upper_bound)
            )
            
            outlier_count += outlier_mask.sum()
                
        # 移除异常值
        valid_mask = True
        for road_id in self.cleaned_data['路段ID'].unique():
            road_mask = self.cleaned_data['路段ID'] == road_id
            road_data = self.cleaned_data[road_mask]['通行时间(秒)']
            
            Q1 = road_data.quantile(0.25)
            Q3 = road_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            road_valid_mask = ~road_mask | (
                (self.cleaned_data['通行时间(秒)'] >= lower_bound) & 
                (self.cleaned_data['通行时间(秒)'] <= upper_bound)
            )
            valid_mask = valid_mask & road_valid_mask
            
        self.cleaned_data = self.cleaned_data[valid_mask].reset_index(drop=True)
        print(f"检测到 {outlier_count} 个异常值")
        print(f"清洗后数据记录数: {len(self.cleaned_data)}")
        
    def aggregate_by_time_intervals(self, interval_minutes=15):
        """按时间间隔聚合数据"""
        if self.cleaned_data is None:
            raise ValueError("请先进行数据清洗")
            
        # 计算时间间隔索引
        self.cleaned_data['时间间隔'] = self.cleaned_data['时间分钟'] // interval_minutes
        
        # 按路段和时间间隔聚合
        aggregated = self.cleaned_data.groupby(['路段ID', '时间间隔']).agg({
            '通行时间(秒)': ['mean', 'std', 'min', 'max', 'count'],
            '速度': ['mean', 'std'],
            '距离(米)': 'first'
        }).reset_index()
        
        # 扁平化列名
        aggregated.columns = [
            '路段ID', '时间间隔', 
            '通行时间_均值', '通行时间_标准差', '通行时间_最小值', '通行时间_最大值', '样本数量',
            '速度_均值', '速度_标准差', '距离'
        ]
        
        # 过滤样本数量过少的时间间隔
        aggregated = aggregated[aggregated['样本数量'] >= 3].reset_index(drop=True)
        
        return aggregated

class TimeSegmentPartitioner:
    """时段划分器"""
    
    def __init__(self, min_samples_per_segment=5):
        self.min_samples_per_segment = min_samples_per_segment
        self.segments = {}
        
    def partition_by_statistical_test(self, road_data, significance_level=0.05):
        """基于统计检验的时段划分方法"""
        time_intervals = sorted(road_data['时间间隔'].unique())
        
        if len(time_intervals) < 2:
            return [time_intervals]
            
        segments = []
        current_segment = [time_intervals[0]]
        
        for i in range(1, len(time_intervals)):
            current_interval = time_intervals[i]
            
            # 获取当前时段和前一时段的数据
            current_data = road_data[road_data['时间间隔'] == current_interval]['通行时间_均值'].values
            previous_data = road_data[road_data['时间间隔'].isin(current_segment)]['通行时间_均值'].values
            
            # 进行t检验 (如果样本数量足够)
            if len(current_data) > 0 and len(previous_data) > 1:
                try:
                    t_stat, p_value = stats.ttest_ind(current_data, previous_data)
                    
                    if p_value < significance_level:
                        # 显著差异，开始新时段
                        segments.append(current_segment)
                        current_segment = [current_interval]
                    else:
                        # 无显著差异，合并到当前时段
                        current_segment.append(current_interval)
                except:
                    # 如果统计检验失败，直接合并
                    current_segment.append(current_interval)
            else:
                current_segment.append(current_interval)
                
        segments.append(current_segment)
        
        # 合并过小的时段
        merged_segments = []
        for segment in segments:
            if len(segment) >= 1:  # 至少包含1个时间间隔
                merged_segments.append(segment)
            elif merged_segments:
                # 合并到前一个时段
                merged_segments[-1].extend(segment)
            else:
                # 如果是第一个时段，保留
                merged_segments.append(segment)
                
        return merged_segments if merged_segments else [time_intervals]

class ImpedanceIntervalCalculator:
    """阻抗区间计算器"""
    
    def __init__(self, confidence_level=0.95):
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level
        
    def calculate_interval(self, segment_data, original_data):
        """计算时段阻抗区间"""
        if len(segment_data) == 0:
            return None
            
        # 获取原始数据用于计算区间
        segment_intervals = segment_data['时间间隔'].tolist()
        raw_travel_times = []
        
        for interval in segment_intervals:
            interval_data = original_data[original_data['时间间隔'] == interval]['通行时间(秒)']
            raw_travel_times.extend(interval_data.tolist())
            
        if len(raw_travel_times) < 2:
            return None
            
        travel_times = np.array(raw_travel_times)
        n = len(travel_times)
        
        # 基本统计量
        mean = np.mean(travel_times)
        std = np.std(travel_times, ddof=1)
        
        # 方法1：基于正态分布的置信区间
        t_critical = stats.t.ppf(1 - self.alpha/2, df=n-1)
        margin_error = t_critical * std / np.sqrt(n)
        
        normal_interval = {
            'lower': max(0, mean - margin_error),
            'upper': mean + margin_error,
            'method': 'normal_confidence'
        }
        
        # 方法2：基于分位数的区间
        percentile_interval = {
            'lower': np.percentile(travel_times, (self.alpha/2) * 100),
            'upper': np.percentile(travel_times, (1 - self.alpha/2) * 100),
            'method': 'percentile'
        }
        
        # 方法3：基于经验分布的区间
        empirical_interval = {
            'lower': np.min(travel_times),
            'upper': np.max(travel_times),
            'method': 'empirical'
        }
        
        return {
            'normal': normal_interval,
            'percentile': percentile_interval,
            'empirical': empirical_interval,
            'statistics': {
                'mean': mean,
                'std': std,
                'n': n,
                'cv': std / mean if mean > 0 else float('inf'),
                'median': np.median(travel_times)
            }
        }
