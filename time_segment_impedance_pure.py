#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时段区间阻抗系统纯Python实现
基于历史交通数据的时段区间阻抗划分方法
不依赖任何外部库，仅使用Python标准库
"""

import csv
import math
from datetime import datetime
from collections import defaultdict

class TimeSegmentImpedanceSystem:
    """时段区间阻抗系统主类"""
    
    def __init__(self, confidence_level=0.95):
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level
        self.road_segments = {}
        self.original_data = []
        
    def load_and_preprocess_data(self, file_paths):
        """加载和预处理数据"""
        print("=== 开始数据预处理 ===")
        
        # 1. 加载数据
        all_data = []
        for file_path in file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    file_data = list(reader)
                    all_data.extend(file_data)
                    print(f"成功加载文件: {file_path}, 记录数: {len(file_data)}")
            except Exception as e:
                print(f"加载文件失败 {file_path}: {e}")
                
        if not all_data:
            raise ValueError("没有成功加载任何数据文件")
            
        print(f"总计加载记录数: {len(all_data)}")
        
        # 2. 数据清洗和转换
        cleaned_data = []
        outlier_count = 0
        
        # 按路段分组处理
        road_data_groups = defaultdict(list)
        for row in all_data:
            try:
                road_id = row['路段ID']
                travel_time = float(row['通行时间(秒)'])
                distance = float(row['距离(米)'])
                
                # 解析时间
                time_str = row['采集时间']
                if 'T' in time_str:
                    time_part = time_str.split('T')[1].split('.')[0]
                else:
                    time_part = time_str.split(' ')[1]
                    
                hour, minute, second = map(int, time_part.split(':'))
                time_minutes = hour * 60 + minute
                
                # 计算速度
                speed = distance / travel_time if travel_time > 0 else 0
                
                road_data_groups[road_id].append({
                    'road_id': road_id,
                    'travel_time': travel_time,
                    'distance': distance,
                    'speed': speed,
                    'time_minutes': time_minutes,
                    'time_interval': time_minutes // 15  # 15分钟间隔
                })
            except (ValueError, KeyError) as e:
                continue
        
        # 3. 异常值处理（IQR方法）
        for road_id, road_data in road_data_groups.items():
            if len(road_data) < 4:
                continue
                
            travel_times = [d['travel_time'] for d in road_data]
            travel_times.sort()
            
            n = len(travel_times)
            q1_idx = n // 4
            q3_idx = 3 * n // 4
            
            q1 = travel_times[q1_idx]
            q3 = travel_times[q3_idx]
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            # 过滤异常值
            before_count = len(road_data)
            road_data = [d for d in road_data if lower_bound <= d['travel_time'] <= upper_bound]
            outlier_count += before_count - len(road_data)
            
            cleaned_data.extend(road_data)
        
        self.original_data = cleaned_data
        print(f"移除 {outlier_count} 个异常值")
        print(f"清洗后数据记录数: {len(self.original_data)}")
        
        return self.original_data
        
    def calculate_statistics(self, values):
        """计算基本统计量"""
        if not values:
            return None
            
        n = len(values)
        mean = sum(values) / n
        
        # 计算标准差
        variance = sum((x - mean) ** 2 for x in values) / (n - 1) if n > 1 else 0
        std = math.sqrt(variance)
        
        # 计算分位数
        sorted_values = sorted(values)
        
        def percentile(data, p):
            k = (len(data) - 1) * p
            f = math.floor(k)
            c = math.ceil(k)
            if f == c:
                return data[int(k)]
            d0 = data[int(f)] * (c - k)
            d1 = data[int(c)] * (k - f)
            return d0 + d1
        
        return {
            'mean': mean,
            'std': std,
            'n': n,
            'min': min(values),
            'max': max(values),
            'median': percentile(sorted_values, 0.5),
            'q25': percentile(sorted_values, 0.25),
            'q75': percentile(sorted_values, 0.75),
            'p025': percentile(sorted_values, 0.025),
            'p975': percentile(sorted_values, 0.975),
            'cv': std / mean if mean > 0 else float('inf')
        }
        
    def simple_t_test(self, group1, group2):
        """简化的t检验"""
        if len(group1) < 2 or len(group2) < 2:
            return False, 1.0
            
        stats1 = self.calculate_statistics(group1)
        stats2 = self.calculate_statistics(group2)
        
        if stats1['std'] == 0 and stats2['std'] == 0:
            return stats1['mean'] != stats2['mean'], 0.0 if stats1['mean'] != stats2['mean'] else 1.0
        
        # 简化的t统计量计算
        pooled_std = math.sqrt(((stats1['n'] - 1) * stats1['std']**2 + (stats2['n'] - 1) * stats2['std']**2) / 
                              (stats1['n'] + stats2['n'] - 2))
        
        if pooled_std == 0:
            return stats1['mean'] != stats2['mean'], 0.0 if stats1['mean'] != stats2['mean'] else 1.0
            
        t_stat = abs(stats1['mean'] - stats2['mean']) / (pooled_std * math.sqrt(1/stats1['n'] + 1/stats2['n']))
        
        # 简化的p值估计（基于t分布的近似）
        # 对于大样本，t分布接近标准正态分布
        if t_stat > 2.0:  # 大致对应p < 0.05
            return True, 0.01
        elif t_stat > 1.5:
            return False, 0.1
        else:
            return False, 0.5
        
    def partition_time_segments(self, road_data, significance_level=0.05):
        """基于统计检验的时段划分"""
        # 按时间间隔分组
        interval_groups = defaultdict(list)
        for data in road_data:
            interval_groups[data['time_interval']].append(data['travel_time'])
        
        time_intervals = sorted(interval_groups.keys())
        
        if len(time_intervals) < 2:
            return [time_intervals]
            
        segments = []
        current_segment = [time_intervals[0]]
        
        for i in range(1, len(time_intervals)):
            current_interval = time_intervals[i]
            
            # 获取当前间隔和之前间隔的通行时间数据
            current_times = interval_groups[current_interval]
            previous_times = []
            for interval in current_segment:
                previous_times.extend(interval_groups[interval])
            
            # 简化的t检验判断是否有显著差异
            if len(current_times) > 1 and len(previous_times) > 1:
                is_significant, p_value = self.simple_t_test(current_times, previous_times)
                
                if is_significant and p_value < significance_level:
                    # 显著差异，开始新时段
                    segments.append(current_segment)
                    current_segment = [current_interval]
                else:
                    # 无显著差异，合并到当前时段
                    current_segment.append(current_interval)
            else:
                current_segment.append(current_interval)
                
        segments.append(current_segment)
        return segments
        
    def calculate_impedance_interval(self, travel_times):
        """计算阻抗区间"""
        if len(travel_times) < 2:
            return None
            
        stats = self.calculate_statistics(travel_times)
        
        # 基于分位数的区间（更稳健）
        percentile_interval = {
            'lower': stats['p025'],
            'upper': stats['p975'],
            'width': stats['p975'] - stats['p025']
        }
        
        # 基于均值±2标准差的区间
        normal_interval = {
            'lower': max(0, stats['mean'] - 2 * stats['std']),
            'upper': stats['mean'] + 2 * stats['std'],
            'width': 4 * stats['std']
        }
        
        return {
            'percentile': percentile_interval,
            'normal': normal_interval,
            'statistics': stats
        }
        
    def build_system(self, data_files):
        """构建完整的时段区间阻抗系统"""
        print("=== 构建时段区间阻抗系统 ===")
        
        # 1. 数据预处理
        self.load_and_preprocess_data(data_files)
        
        # 2. 按路段分组
        road_groups = defaultdict(list)
        for data in self.original_data:
            road_groups[data['road_id']].append(data)
        
        print(f"\n开始处理 {len(road_groups)} 个路段...")
        
        processed_count = 0
        for road_id, road_data in road_groups.items():
            if len(road_data) < 10:  # 数据太少，跳过
                continue
                
            # 时段划分
            segments = self.partition_time_segments(road_data)
            
            # 计算每个时段的阻抗区间
            road_intervals = {}
            for j, segment_intervals in enumerate(segments):
                segment_travel_times = []
                for data in road_data:
                    if data['time_interval'] in segment_intervals:
                        segment_travel_times.append(data['travel_time'])
                
                if len(segment_travel_times) < 3:  # 时段数据太少
                    continue
                    
                interval_result = self.calculate_impedance_interval(segment_travel_times)
                
                if interval_result:
                    start_time = min(segment_intervals) * 15  # 转换为分钟
                    end_time = (max(segment_intervals) + 1) * 15
                    
                    road_intervals[f'segment_{j}'] = {
                        'time_intervals': segment_intervals,
                        'impedance_intervals': interval_result,
                        'start_time': start_time,
                        'end_time': end_time,
                        'start_time_str': f"{start_time//60:02d}:{start_time%60:02d}",
                        'end_time_str': f"{end_time//60:02d}:{end_time%60:02d}"
                    }
            
            if road_intervals:
                self.road_segments[road_id] = road_intervals
                processed_count += 1
                
            if processed_count % 10 == 0:
                print(f"已处理 {processed_count} 个路段")
                
        print(f"成功处理 {len(self.road_segments)} 个路段")
        
    def get_impedance_interval(self, road_id, current_time_minutes, method='percentile'):
        """获取指定道路在指定时间的阻抗区间"""
        if road_id not in self.road_segments:
            return None
            
        road_data = self.road_segments[road_id]
        
        for segment_info in road_data.values():
            if segment_info['start_time'] <= current_time_minutes < segment_info['end_time']:
                return segment_info['impedance_intervals'][method]
                
        return None

    def compare_with_traditional_method(self, road_id):
        """与传统区间阻抗方法对比"""
        if road_id not in self.road_segments:
            return None

        # 获取原始数据
        road_travel_times = []
        for data in self.original_data:
            if data['road_id'] == road_id:
                road_travel_times.append(data['travel_time'])

        if len(road_travel_times) == 0:
            return None

        # 传统方法：整体区间
        stats = self.calculate_statistics(road_travel_times)
        traditional_interval = {
            'lower': stats['p025'],
            'upper': stats['p975'],
            'width': stats['p975'] - stats['p025']
        }

        # 时段区间方法：各时段区间
        segment_intervals = []
        for segment_info in self.road_segments[road_id].values():
            interval_data = segment_info['impedance_intervals']['percentile']
            segment_intervals.append({
                'lower': interval_data['lower'],
                'upper': interval_data['upper'],
                'width': interval_data['width'],
                'time_range': f"{segment_info['start_time_str']}-{segment_info['end_time_str']}"
            })

        # 计算改进指标
        if segment_intervals:
            avg_segment_width = sum(seg['width'] for seg in segment_intervals) / len(segment_intervals)
            width_reduction = (traditional_interval['width'] - avg_segment_width) / traditional_interval['width'] * 100
        else:
            avg_segment_width = traditional_interval['width']
            width_reduction = 0

        return {
            'road_id': road_id,
            'traditional': traditional_interval,
            'segments': segment_intervals,
            'improvement': {
                'width_reduction_percent': width_reduction,
                'avg_segment_width': avg_segment_width,
                'num_segments': len(segment_intervals)
            }
        }

    def generate_summary_report(self):
        """生成摘要报告"""
        print("\n" + "="*60)
        print("时段区间阻抗系统分析报告")
        print("="*60)

        print(f"\n1. 系统概览")
        print(f"   处理路段数量: {len(self.road_segments)}")
        print(f"   原始数据记录数: {len(self.original_data)}")

        # 统计时段数量
        total_segments = sum(len(segments) for segments in self.road_segments.values())
        avg_segments = total_segments / len(self.road_segments) if self.road_segments else 0

        print(f"\n2. 时段划分统计")
        print(f"   总时段数量: {total_segments}")
        print(f"   平均每路段时段数: {avg_segments:.1f}")

        # 计算改进效果
        improvements = []
        sample_roads = list(self.road_segments.keys())[:10]  # 取前10个路段进行分析

        for road_id in sample_roads:
            comparison = self.compare_with_traditional_method(road_id)
            if comparison and comparison['improvement']['width_reduction_percent'] > 0:
                improvements.append(comparison['improvement']['width_reduction_percent'])

        if improvements:
            avg_improvement = sum(improvements) / len(improvements)
            max_improvement = max(improvements)
            min_improvement = min(improvements)

            print(f"\n3. 改进效果（基于前10个路段样本）")
            print(f"   平均区间宽度减少: {avg_improvement:.1f}%")
            print(f"   最大区间宽度减少: {max_improvement:.1f}%")
            print(f"   最小区间宽度减少: {min_improvement:.1f}%")

        # 详细路段示例
        print(f"\n4. 路段详细示例（前5个路段）")
        sample_roads = list(self.road_segments.keys())[:5]

        for road_id in sample_roads:
            print(f"\n   路段 {road_id}:")
            road_data = self.road_segments[road_id]
            print(f"     时段数量: {len(road_data)}")

            for segment_name, segment_info in road_data.items():
                stats = segment_info['impedance_intervals']['statistics']
                interval = segment_info['impedance_intervals']['percentile']
                print(f"     {segment_info['start_time_str']}-{segment_info['end_time_str']}: "
                      f"均值={stats['mean']:.1f}秒, "
                      f"区间=[{interval['lower']:.1f}, {interval['upper']:.1f}]秒")

        print(f"\n5. 实时查询示例")
        test_times = [7*60+30, 8*60, 8*60+30]  # 7:30, 8:00, 8:30

        for road_id in sample_roads[:3]:
            print(f"\n   路段 {road_id}:")
            for time_minutes in test_times:
                hour = time_minutes // 60
                minute = time_minutes % 60
                interval = self.get_impedance_interval(road_id, time_minutes)

                if interval:
                    print(f"     {hour:02d}:{minute:02d} - 阻抗区间: [{interval['lower']:.1f}, {interval['upper']:.1f}]秒")
                else:
                    print(f"     {hour:02d}:{minute:02d} - 无数据")

        # 保存详细报告到文件
        self.save_detailed_report()

    def save_detailed_report(self):
        """保存详细报告到文件"""
        try:
            with open('时段区间阻抗分析报告.txt', 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("时段区间阻抗系统详细分析报告\n")
                f.write("=" * 60 + "\n\n")

                # 系统概览
                f.write("1. 系统概览\n")
                f.write("-" * 30 + "\n")
                f.write(f"处理路段数量: {len(self.road_segments)}\n")
                f.write(f"原始数据记录数: {len(self.original_data)}\n\n")

                # 详细路段信息
                f.write("2. 路段详细信息\n")
                f.write("-" * 30 + "\n")

                for road_id in sorted(self.road_segments.keys()):
                    f.write(f"\n路段 {road_id}:\n")
                    road_data = self.road_segments[road_id]
                    f.write(f"  时段数量: {len(road_data)}\n")

                    for segment_name, segment_info in road_data.items():
                        stats = segment_info['impedance_intervals']['statistics']
                        interval = segment_info['impedance_intervals']['percentile']
                        f.write(f"  {segment_info['start_time_str']}-{segment_info['end_time_str']}: ")
                        f.write(f"均值={stats['mean']:.1f}秒, ")
                        f.write(f"标准差={stats['std']:.1f}秒, ")
                        f.write(f"区间=[{interval['lower']:.1f}, {interval['upper']:.1f}]秒, ")
                        f.write(f"样本数={stats['n']}\n")

                # 改进效果分析
                f.write("\n3. 改进效果分析\n")
                f.write("-" * 30 + "\n")

                for road_id in list(self.road_segments.keys())[:20]:  # 分析前20个路段
                    comparison = self.compare_with_traditional_method(road_id)
                    if comparison:
                        f.write(f"\n路段 {road_id}:\n")
                        f.write(f"  传统方法区间宽度: {comparison['traditional']['width']:.1f}秒\n")
                        f.write(f"  时段方法平均宽度: {comparison['improvement']['avg_segment_width']:.1f}秒\n")
                        f.write(f"  区间宽度减少: {comparison['improvement']['width_reduction_percent']:.1f}%\n")
                        f.write(f"  时段数量: {comparison['improvement']['num_segments']}\n")

            print("   详细报告已保存到: 时段区间阻抗分析报告.txt")

        except Exception as e:
            print(f"   保存报告失败: {e}")

def main():
    """主函数"""
    print("时段区间阻抗系统演示")
    print("=" * 50)

    # 数据文件路径
    data_files = [
        '采集数据/早高峰/augment7.28早高峰（28晚改的）.csv',
        '采集数据/早高峰/augment7.29早高峰.csv',
        '采集数据/早高峰/augment7.30早高峰.csv',
        '采集数据/早高峰/augment7.31早高峰.csv'
    ]

    # 检查文件是否存在
    import os
    existing_files = [f for f in data_files if os.path.exists(f)]

    if not existing_files:
        print("错误：找不到数据文件")
        print("请确保以下文件存在：")
        for f in data_files:
            print(f"  - {f}")
        return

    print(f"找到 {len(existing_files)} 个数据文件")

    # 创建系统实例
    system = TimeSegmentImpedanceSystem(confidence_level=0.95)

    try:
        # 构建系统
        system.build_system(existing_files)

        # 生成报告
        system.generate_summary_report()

        print("\n系统构建完成！")

    except Exception as e:
        print(f"系统构建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
