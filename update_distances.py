#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新CSV文件中的距离数据
将augment7.3000晚高峰.csv中每个路段的距离更新为augment7.3111晚高峰.csv中对应路段的距离
"""

import pandas as pd
import os

def update_distances():
    """更新距离数据"""
    
    # 文件路径
    source_file = "data_output/augment7.3111晚高峰.csv"  # 距离数据来源文件
    target_file = "data_output/augment7.3000晚高峰.csv"  # 需要更新的文件
    backup_file = "data_output/augment7.3000晚高峰_backup.csv"  # 备份文件
    
    print("=== 开始更新距离数据 ===")
    
    try:
        # 1. 读取源文件（距离数据来源）
        print(f"读取源文件: {source_file}")
        source_df = pd.read_csv(source_file, encoding='utf-8')
        print(f"源文件记录数: {len(source_df)}")
        print(f"源文件列名: {list(source_df.columns)}")
        
        # 2. 读取目标文件（需要更新的文件）
        print(f"\n读取目标文件: {target_file}")
        target_df = pd.read_csv(target_file, encoding='utf-8')
        print(f"目标文件记录数: {len(target_df)}")
        print(f"目标文件列名: {list(target_df.columns)}")
        
        # 3. 创建备份
        print(f"\n创建备份文件: {backup_file}")
        target_df.to_csv(backup_file, index=False, encoding='utf-8')
        
        # 4. 从源文件中提取每个路段的距离（取第一个值，因为同一路段的距离应该是相同的）
        print("\n提取路段距离映射...")
        distance_mapping = source_df.groupby('路段ID')['距离(米)'].first().to_dict()
        print(f"找到 {len(distance_mapping)} 个路段的距离数据")
        
        # 5. 显示距离映射
        print("\n路段距离映射:")
        for road_id, distance in sorted(distance_mapping.items()):
            print(f"  {road_id}: {distance} 米")
        
        # 6. 更新目标文件中的距离
        print("\n开始更新距离...")
        updated_count = 0
        not_found_roads = set()
        
        for idx, row in target_df.iterrows():
            road_id = row['路段ID']
            if road_id in distance_mapping:
                old_distance = row['距离(米)']
                new_distance = distance_mapping[road_id]
                target_df.at[idx, '距离(米)'] = new_distance
                if old_distance != new_distance:
                    updated_count += 1
            else:
                not_found_roads.add(road_id)
        
        print(f"更新了 {updated_count} 条记录的距离")
        
        if not_found_roads:
            print(f"\n警告: 以下路段在源文件中未找到距离数据:")
            for road_id in sorted(not_found_roads):
                print(f"  {road_id}")
        
        # 7. 保存更新后的文件
        print(f"\n保存更新后的文件: {target_file}")
        target_df.to_csv(target_file, index=False, encoding='utf-8')
        
        # 8. 验证更新结果
        print("\n验证更新结果...")
        verification_df = pd.read_csv(target_file, encoding='utf-8')
        
        print("更新后的路段距离统计:")
        road_distances = verification_df.groupby('路段ID')['距离(米)'].first().sort_index()
        for road_id, distance in road_distances.items():
            print(f"  {road_id}: {distance} 米")
        
        print(f"\n=== 更新完成 ===")
        print(f"原文件已备份为: {backup_file}")
        print(f"更新后文件: {target_file}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = update_distances()
    if success:
        print("\n距离更新成功完成！")
    else:
        print("\n距离更新失败！")
