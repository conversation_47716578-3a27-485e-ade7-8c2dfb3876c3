#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时段区间阻抗系统演示 - 特定路段详细分析
"""

from time_segment_impedance_pure import TimeSegmentImpedanceSystem

def analyze_specific_road(system, road_id):
    """分析特定路段的时段区间阻抗"""
    print(f"\n{'='*60}")
    print(f"路段 {road_id} 详细分析")
    print(f"{'='*60}")
    
    if road_id not in system.road_segments:
        print(f"路段 {road_id} 不存在于系统中")
        return
    
    # 1. 基本信息
    road_data = system.road_segments[road_id]
    print(f"\n1. 基本信息")
    print(f"   时段数量: {len(road_data)}")
    
    # 2. 各时段详细信息
    print(f"\n2. 各时段详细信息")
    for i, (segment_name, segment_info) in enumerate(road_data.items(), 1):
        stats = segment_info['impedance_intervals']['statistics']
        percentile_interval = segment_info['impedance_intervals']['percentile']
        normal_interval = segment_info['impedance_intervals']['normal']
        
        print(f"\n   时段 {i}: {segment_info['start_time_str']} - {segment_info['end_time_str']}")
        print(f"     样本数量: {stats['n']}")
        print(f"     均值: {stats['mean']:.1f} 秒")
        print(f"     标准差: {stats['std']:.1f} 秒")
        print(f"     变异系数: {stats['cv']:.3f}")
        print(f"     中位数: {stats['median']:.1f} 秒")
        print(f"     分位数区间 (95%): [{percentile_interval['lower']:.1f}, {percentile_interval['upper']:.1f}] 秒")
        print(f"     正态区间 (95%): [{normal_interval['lower']:.1f}, {normal_interval['upper']:.1f}] 秒")
        print(f"     区间宽度: {percentile_interval['width']:.1f} 秒")
    
    # 3. 与传统方法对比
    comparison = system.compare_with_traditional_method(road_id)
    if comparison:
        print(f"\n3. 与传统方法对比")
        print(f"   传统方法 (整体95%区间):")
        print(f"     区间: [{comparison['traditional']['lower']:.1f}, {comparison['traditional']['upper']:.1f}] 秒")
        print(f"     宽度: {comparison['traditional']['width']:.1f} 秒")
        
        print(f"\n   时段方法:")
        print(f"     平均区间宽度: {comparison['improvement']['avg_segment_width']:.1f} 秒")
        print(f"     区间宽度减少: {comparison['improvement']['width_reduction_percent']:.1f}%")
        
        print(f"\n   各时段区间:")
        for segment in comparison['segments']:
            print(f"     {segment['time_range']}: [{segment['lower']:.1f}, {segment['upper']:.1f}] 秒 (宽度: {segment['width']:.1f})")
    
    # 4. 实时查询示例
    print(f"\n4. 实时查询示例")
    test_times = [
        (7, 0),   # 7:00
        (7, 15),  # 7:15
        (7, 30),  # 7:30
        (7, 45),  # 7:45
        (8, 0),   # 8:00
        (8, 15),  # 8:15
        (8, 30),  # 8:30
        (8, 45),  # 8:45
    ]
    
    for hour, minute in test_times:
        time_minutes = hour * 60 + minute
        interval = system.get_impedance_interval(road_id, time_minutes)
        
        if interval:
            print(f"   {hour:02d}:{minute:02d} - 阻抗区间: [{interval['lower']:.1f}, {interval['upper']:.1f}] 秒")
        else:
            print(f"   {hour:02d}:{minute:02d} - 无数据")

def compare_multiple_roads(system, road_ids):
    """对比多个路段的时段划分效果"""
    print(f"\n{'='*60}")
    print(f"多路段对比分析")
    print(f"{'='*60}")
    
    print(f"\n路段对比表:")
    print(f"{'路段ID':<10} {'时段数':<8} {'传统宽度':<12} {'时段宽度':<12} {'改进率':<10}")
    print(f"{'-'*60}")
    
    improvements = []
    
    for road_id in road_ids:
        if road_id not in system.road_segments:
            continue
            
        comparison = system.compare_with_traditional_method(road_id)
        if comparison:
            traditional_width = comparison['traditional']['width']
            segment_width = comparison['improvement']['avg_segment_width']
            improvement = comparison['improvement']['width_reduction_percent']
            num_segments = comparison['improvement']['num_segments']
            
            print(f"{road_id:<10} {num_segments:<8} {traditional_width:<12.1f} {segment_width:<12.1f} {improvement:<10.1f}%")
            
            if improvement > 0:
                improvements.append(improvement)
    
    if improvements:
        print(f"\n总体改进效果:")
        print(f"  平均改进率: {sum(improvements)/len(improvements):.1f}%")
        print(f"  最大改进率: {max(improvements):.1f}%")
        print(f"  最小改进率: {min(improvements):.1f}%")
        print(f"  有效路段数: {len(improvements)}/{len(road_ids)}")

def demonstrate_time_segment_benefits():
    """演示时段区间阻抗的优势"""
    print(f"\n{'='*60}")
    print(f"时段区间阻抗系统优势演示")
    print(f"{'='*60}")
    
    print(f"\n传统区间阻抗方法的问题:")
    print(f"1. 累加效应: 多个路段的区间阻抗累加后，区间过宽，失去实用价值")
    print(f"2. 时间不敏感: 无法反映不同时段的交通流量变化")
    print(f"3. 精度不足: 整体统计掩盖了时段内的差异性")
    
    print(f"\n时段区间阻抗方法的优势:")
    print(f"1. 时间敏感: 根据交通流量的时间变化规律进行时段划分")
    print(f"2. 精度提升: 各时段内的阻抗区间更加精确")
    print(f"3. 累加优化: 减少了区间累加时的宽度增长")
    print(f"4. 实用性强: 为路径规划提供更准确的时间估计")
    
    print(f"\n技术特点:")
    print(f"1. 统计检验: 使用t检验判断时段间的显著性差异")
    print(f"2. 异常值处理: IQR方法过滤异常数据")
    print(f"3. 多种区间: 提供分位数区间和正态区间两种计算方法")
    print(f"4. 实时查询: 支持根据当前时间快速获取阻抗区间")

def main():
    """主演示函数"""
    print("时段区间阻抗系统详细演示")
    print("=" * 50)
    
    # 数据文件路径
    data_files = [
        '采集数据/早高峰/augment7.28早高峰（28晚改的）.csv',
        '采集数据/早高峰/augment7.29早高峰.csv',
        '采集数据/早高峰/augment7.30早高峰.csv',
        '采集数据/早高峰/augment7.31早高峰.csv'
    ]
    
    # 检查文件是否存在
    import os
    existing_files = [f for f in data_files if os.path.exists(f)]
    
    if not existing_files:
        print("错误：找不到数据文件")
        return
    
    # 创建系统实例
    system = TimeSegmentImpedanceSystem(confidence_level=0.95)
    
    try:
        # 构建系统
        print("正在构建时段区间阻抗系统...")
        system.build_system(existing_files)
        
        # 演示系统优势
        demonstrate_time_segment_benefits()
        
        # 选择几个代表性路段进行详细分析
        sample_roads = ['1-2', '1-9', '10-18', '11-12', '15-16']
        
        # 详细分析特定路段
        for road_id in sample_roads:
            if road_id in system.road_segments:
                analyze_specific_road(system, road_id)
                break  # 只演示第一个存在的路段
        
        # 多路段对比
        all_roads = list(system.road_segments.keys())[:10]  # 取前10个路段
        compare_multiple_roads(system, all_roads)
        
        print(f"\n{'='*60}")
        print("演示完成！")
        print("详细报告已保存在: 时段区间阻抗分析报告.txt")
        print(f"{'='*60}")
        
    except Exception as e:
        print(f"演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
